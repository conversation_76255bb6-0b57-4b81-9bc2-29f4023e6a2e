package com.ruoyi.xyd6fy;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mospital.bsoft.BSoftService;
import org.mospital.bsoft.PatientInfo;
import org.mospital.bsoft.Result;
import org.mospital.bsoft.ZhuyuanPatient;

import static org.junit.jupiter.api.Assertions.assertTrue;

class BsoftServiceTest {

    @Test
    @Disabled("手动执行")
    void testGetPatientInfoByJzCardNo() {
        Result<PatientInfo> result = BSoftService.Companion.getPatientInfoByJzCardNo("**********");
        assertTrue(result.isOk(), result.getMessage());
    }

    @Test
    @Disabled("手动执行")
    void testGetZhuyuanPatientByAdmissionNumber() {
        Result<ZhuyuanPatient> result = BSoftService.Companion.getZhuyuanPatientByAdmissionNumber("266141");
        assertTrue(result.isOk(), result.getMessage());
    }
    
}
