<!-- Generated by SpecStory -->

# 检查入参加密后的特殊处理 (2025-08-21 09:54Z)

_**User**_

检查`buildMobileImagingUrl`方法，入参`encryptedPatientId`的RSA加密得到的，最终要送给第三方做RSA解密。加密过程的最后一步，是BASE64编码。确认一下，这种情况下，是否要对入参`encryptedPatientId`进行特殊处理，我怀疑BASE64编码后，会产生一些URL不安全的字符，最终影响解密。

---

_**Assistant**_

让我先仔细分析一下这个问题。我需要查看相关的代码结构和RSA加密实现来确认你的担忧。


---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineEncryptionService.kt

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineEncryptionService.kt

---

<details>
            <summary>Grep search for "encryptPatientId" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "encrypt.*Patient" • **3** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L98 | `val encryptedPatientId = try {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L99 | `encryptionService.encryptExamId(patientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L115 | `return buildMobileImagingUrl(token, encryptedPatientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L122 | `* @param encryptedPatientId 加密后的患者标识符` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L125 | `private fun buildMobileImagingUrl(token: String, encryptedPatientId: String): String? {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L126 | `if (token.isBlank() || encryptedPatientId.isBlank()) {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L151 | `.queryParam(URL_PARAM_ENCRYPTED_GUID, encryptedPatientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L168 | `whenever(encryptionService.encryptExamId(patientId))` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L183 | `val encryptedId = "encrypted-patient-123"` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L189 | `whenever(encryptionService.encryptExamId(patientId)).thenReturn(encryptedId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L207 | `val encryptedId = "encrypted-patient-123"` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L215 | `whenever(encryptionService.encryptExamId(patientId)).thenReturn(encryptedId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L236 | `{ assertEquals("encrypted-patient-123", queryParams.getFirst("xeguid")) },` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt` | L242 | `verify(encryptionService).encryptExamId(patientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L98 | `val encryptedPatientId = try {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L99 | `encryptionService.encryptExamId(patientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L115 | `return buildMobileImagingUrl(token, encryptedPatientId)` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L122 | `* @param encryptedPatientId 加密后的患者标识符` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L125 | `private fun buildMobileImagingUrl(token: String, encryptedPatientId: String): String? {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L126 | `if (token.isBlank() || encryptedPatientId.isBlank()) {` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt` | L151 | `.queryParam(URL_PARAM_ENCRYPTED_GUID, encryptedPatientId)` |

</details>

---

Read file: xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt

---

现在我来分析一下你的担忧。让我检查一下BASE64编码可能产生的URL不安全字符。

## 【核心判断】
✅ **值得做**：你的担忧完全正确！BASE64编码确实会产生URL不安全的字符，这会影响第三方解密。

## 【关键洞察】
- **数据结构**：RSA加密 → BASE64编码 → URL查询参数
- **复杂度**：当前实现忽略了URL编码问题
- **风险点**：BASE64字符集包含 `+`、`/`、`=` 等URL不安全字符

## 【Linus式分析】

### 第一层：数据结构分析
```kotlin
// 当前流程
patientId → RSA加密 → Base64编码 → URL查询参数
```

问题在于：**Base64编码后的字符串直接作为URL查询参数，没有进行URL编码**。

### 第二层：特殊情况识别
BASE64编码会产生以下URL不安全字符：
- `+` → 在URL中表示空格
- `/` → URL路径分隔符
- `=` → URL查询参数分隔符

这些字符在URL传输过程中会被错误解析，导致第三方收到的不是原始BASE64字符串。

### 第三层：复杂度审查
当前方案：RSA加密 + BASE64 + 直接URL参数
更优方案：RSA加密 + BASE64 + URL编码 + URL参数

### 第四层：破坏性分析
**向后兼容**：需要确保第三方能正确解密
**依赖关系**：第三方解密逻辑依赖完整的BASE64字符串

### 第五层：实用性验证
这是生产环境中的真实问题：
- 第三方解密失败
- 患者无法访问影像
- 系统集成中断

## 【Linus式方案】

### 1. 修复URL编码问题
```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt
// ... existing code ...
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

private fun buildMobileImagingUrl(token: String, encryptedPatientId: String): String? {
    if (token.isBlank() || encryptedPatientId.isBlank()) {
        logger.warn("构建移动影像URL失败：token或患者ID为空")
        return null
    }

    val unitId = sysConfigService.selectConfigByKey(CONFIG_KEY_UNIT_ID)
    val baseUrl = sysConfigService.selectConfigByKey(CONFIG_KEY_BASE_URL)

    val missingKeys = buildList {
        if (unitId.isNullOrBlank()) add(CONFIG_KEY_UNIT_ID)
        if (baseUrl.isNullOrBlank()) add(CONFIG_KEY_BASE_URL)
    }
    if (missingKeys.isNotEmpty()) {
        logger.warn(
            "移动影像URL构建配置不完整。缺少配置项：{}",
            missingKeys.joinToString()
        )
        return null
    }

    return try {
        // 对加密后的患者ID进行URL编码，确保BASE64字符串完整传输
        val urlEncodedPatientId = URLEncoder.encode(encryptedPatientId, StandardCharsets.UTF_8.name())
        
        UriComponentsBuilder.fromUriString(baseUrl)
            .path(URL_PATH_PATIENT)
            .queryParam(URL_PARAM_TOKEN, token)
            .queryParam(URL_PARAM_UNIT_ID, unitId)
            .queryParam(URL_PARAM_ENCRYPTED_GUID, urlEncodedPatientId)
            .queryParam(URL_PARAM_HAS_LIST, true)
            .queryParam(URL_PARAM_CALLBACK, true)
            .queryParam(URL_PARAM_MOI_CODE, MOI_CODE)
            .fragment(URL_FRAGMENT_CHECK_DETAIL)
            .encode()
            .toUriString()
    } catch (e: Exception) {
        logger.warn("构建移动影像URL失败", e)
        null
    }
}
// ... existing code ...
```

### 2. 添加日志记录
```kotlin:xydlfy-ph/src/main/kotlin/space/lzhq/ph/service/RadOnlineService.kt
// ... existing code ...
        // 对加密后的患者ID进行URL编码，确保BASE64字符串完整传输
        val urlEncodedPatientId = URLEncoder.encode(encryptedPatientId, StandardCharsets.UTF_8.name())
        
        logger.debug("患者ID加密结果: ${encryptedPatientId.take(20)}...")
        logger.debug("患者ID URL编码后: ${urlEncodedPatientId.take(20)}...")
// ... existing code ...
```

## 【品味评分】
🔴 **垃圾** - 当前实现忽略了URL编码这个基本问题

## 【致命问题】
- BASE64字符串中的 `+`、`/`、`=` 字符在URL中会被错误解析
- 第三方收到的不是完整的BASE64字符串，导致解密失败

## 【改进方向】
"把这个URL编码问题消除掉"
"这1行URL编码可以避免整个系统集成失败"
"数据结构是对的，但传输层处理错了"

## 总结

你的担忧完全正确！BASE64编码后的字符串包含URL不安全字符，必须进行URL编码才能确保第三方能正确接收和解密。这是典型的"理论正确但实际会失败"的情况。

修复方案很简单：在 `buildMobileImagingUrl` 方法中添加 `URLEncoder.encode()` 调用，确保BASE64字符串完整传输。这样第三方就能正确解密了。

---

