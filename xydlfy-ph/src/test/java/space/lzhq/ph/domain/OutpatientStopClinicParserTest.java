package space.lzhq.ph.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 门诊停诊消息解析器测试
 */
class OutpatientStopClinicParserTest {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private String readTestResource(String filename) throws IOException {
        return new String(Files.readAllBytes(Paths.get("src/test/resources/" + filename)));
    }

    private void assertBasicFields(OutpatientStopClinic clinic) {
        assertNotNull(clinic.getCreateTime(), "创建时间不能为空");
        assertFalse(clinic.getIsRead(), "初始状态应为未读");
    }

    private void assertPatientInfo(OutpatientStopClinic clinic, String expectedId, String expectedName, String expectedIdCard) {
        assertEquals(expectedId, clinic.getSourcePatientId());
        assertEquals(expectedName, clinic.getName());
        assertEquals(expectedIdCard, clinic.getIdCard());
    }

    private void assertScheduleInfo(OutpatientStopClinic clinic, String expectedScheduleMark,
                                   String expectedAppointmentId, String expectedDept, String expectedDeptName) {
        assertEquals(expectedScheduleMark, clinic.getScheduleMark());
        assertEquals(expectedAppointmentId, clinic.getAppointmentId());
        assertEquals(expectedDept, clinic.getRegisteredDept());
        assertEquals(expectedDeptName, clinic.getRegisteredDeptName());
    }

    private void assertTimeFields(OutpatientStopClinic clinic, String expectedRegisteredTime,
                                 String expectedAppointmentTime, String expectedOperatingTime) {
        assertEquals(LocalDateTime.parse(expectedRegisteredTime, DATE_TIME_FORMATTER), clinic.getRegisteredDateTime());
        assertEquals(LocalDateTime.parse(expectedAppointmentTime, DATE_TIME_FORMATTER), clinic.getAppointmentDateTime());
        assertEquals(LocalDateTime.parse(expectedOperatingTime, DATE_TIME_FORMATTER), clinic.getOperatingDateTime());
    }

    /**
     * 测试正常XML解析
     */
    @Test
    void testParseValidXml() throws IOException {
        String xml = readTestResource("outpatient-stop-clinic-message.xml");
        OutpatientStopClinic clinic = OutpatientStopClinic.Parser.parseFromXml(xml);

        assertNotNull(clinic, "解析结果不能为空");
        assertBasicFields(clinic);

        // 验证患者信息
        assertPatientInfo(clinic, "1200436", "曙琳·陶皮克", "650105202409223343");

        // 验证排班信息
        assertScheduleInfo(clinic, "58018", "1489324", "593", "马兰红");

        // 验证时间字段
        assertTimeFields(clinic, "2025-08-22 19:48:18", "2025-08-26 16:53:00", "2025-08-22 19:59:33");

        // 验证其他字段
        assertEquals("杨晶晶", clinic.getOperatingName());
        assertEquals("您预约的科室已停诊", clinic.getMessageContent());

        // 验证医生字段（XML中为空）
        assertEquals("", clinic.getRegisteredDoctor());
        assertEquals("", clinic.getRegisteredDoctorName());
    }

    /**
     * 测试 getStopType 方法
     */
    @Test
    void testGetStopType() throws IOException {
        String xml = readTestResource("outpatient-stop-clinic-message.xml");
        OutpatientStopClinic clinic = OutpatientStopClinic.Parser.parseFromXml(xml);

        // 默认情况下应该返回"停诊"
        assertEquals("停诊", clinic.getStopType());

        // 测试替诊情况
        clinic.setMessageContent("您预约的医生已替诊");
        assertEquals("替诊", clinic.getStopType());
    }

    /**
     * 测试空字符串和空白字符串 - 应该抛出 IllegalArgumentException
     */
    @ParameterizedTest
    @ValueSource(strings = {"", "   "})
    void testParseEmptyXml(String emptyXml) {
        assertThrows(IllegalArgumentException.class, () ->
            OutpatientStopClinic.Parser.parseFromXml(emptyXml));
    }

    /**
     * 测试无效XML格式和缺少元素 - 应该抛出 RuntimeException
     */
    @ParameterizedTest
    @ValueSource(strings = {
            "<invalid>xml</invalid>",
            "<BSXml><MsgHeader><Organization>test</Organization></MsgHeader></BSXml>",
            "<BSXml><NoVisit><ScheduleMark>123</ScheduleMark></NoVisit></BSXml>"
    })
    void testParseInvalidXml(String invalidXml) {
        assertThrows(RuntimeException.class, () ->
            OutpatientStopClinic.Parser.parseFromXml(invalidXml));
    }

    @Test
    void testParseNullXml() {
        assertThrows(IllegalArgumentException.class, () ->
            OutpatientStopClinic.Parser.parseFromXml(null));
    }

    /**
     * 边界情况测试：验证解析器的容错能力
     */
    @Test
    void testParseBoundaryConditions() throws IOException {
        String xml = readTestResource("outpatient-stop-clinic-message.xml");

        // 测试无效时间格式 - 应该使用当前时间作为默认值
        String invalidTimeXml = xml.replace("<RegisteredDateTime>2025-08-22 19:48:18</RegisteredDateTime>",
                "<RegisteredDateTime>invalid-date</RegisteredDateTime>");
        OutpatientStopClinic clinic = OutpatientStopClinic.Parser.parseFromXml(invalidTimeXml);
        assertNotNull(clinic);
        assertNotNull(clinic.getRegisteredDateTime());

        // 测试缺少Visit元素
        String noVisitXml = xml.replace("<Visit>", "<InvalidElement>")
                              .replace("</Visit>", "</InvalidElement>");
        assertThrows(RuntimeException.class, () ->
            OutpatientStopClinic.Parser.parseFromXml(noVisitXml));
    }

    /**
     * 测试XML字段名拼写错误的兼容性
     * XML中存在拼写错误：RegisteredDeptNane, RegisteredDcotor, RegisteredDcotorName
     */
    @Test
    void testXmlFieldNameTypos() throws IOException {
        String xml = readTestResource("outpatient-stop-clinic-message.xml");
        OutpatientStopClinic clinic = OutpatientStopClinic.Parser.parseFromXml(xml);

        // 验证拼写错误的字段名仍能正确解析
        assertEquals("马兰红", clinic.getRegisteredDeptName()); // 来自 RegisteredDeptNane
        assertEquals("", clinic.getRegisteredDoctor());        // 来自 RegisteredDcotor
        assertEquals("", clinic.getRegisteredDoctorName());    // 来自 RegisteredDcotorName
    }

    /**
     * 测试空字段处理
     */
    @Test
    void testEmptyFields() throws IOException {
        String xml = readTestResource("outpatient-stop-clinic-message.xml");

        // 创建包含空字段的XML
        String emptyFieldXml = xml.replace("<Name>曙琳·陶皮克</Name>", "<Name></Name>")
                                 .replace("<IdCard>650105202409223343</IdCard>", "<IdCard></IdCard>");

        OutpatientStopClinic clinic = OutpatientStopClinic.Parser.parseFromXml(emptyFieldXml);
        assertNotNull(clinic);
        assertEquals("", clinic.getName());
        assertEquals("", clinic.getIdCard());
    }
}
