package space.lzhq.ph.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import space.lzhq.ph.enums.PatientType;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 检查状态消息解析器测试
 */
class InspectionStatusMessageParserTest {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private String readTestResource(String filename) throws IOException {
        return new String(Files.readAllBytes(Paths.get("src/test/resources/" + filename)));
    }

    private void assertBasicMessage(InspectionStatusMessage message) {
        assertNotNull(message.getRawText(), "原始XML不能为空");
        assertNotNull(message.getCreateTime(), "创建时间不能为空");
        assertNotNull(message.getContent(), "消息内容不能为空");
    }

    private void assertPatientInfo(InspectionStatusMessage msg, String expectedId,
                                   String expectedName, PatientType expectedType) {
        assertEquals(expectedId, msg.getPatientId());
        assertEquals(expectedName, msg.getPatientName());
        assertEquals(expectedType, msg.getPatientType());
    }

    private void assertVisitInfo(InspectionStatusMessage msg, String expectedVisitId,
                                 String expectedCardNo, String expectedAdmissionNo) {
        assertEquals(expectedVisitId, msg.getVisitId());
        assertEquals(expectedCardNo, msg.getPatientCardNo());
        assertEquals(expectedAdmissionNo, msg.getAdmissionNumber());
    }

    /**
     * 数据驱动测试：所有有效XML格式的解析
     * 这一个测试替代了原来三个冗余的测试方法
     */
    @ParameterizedTest
    @CsvSource({
            "inspection-status-message-iv.xml, 1249682, 刘中伊, IV, 210607, '', 266141, 895035, 华珊, 2025-08-22 15:22:32, 397676213",
            "inspection-status-message-ov.xml, 926559, 刘昱佟, OV, 1692198, **********, '', 894230, 马预芝, 2025-08-22 00:00:00, 39864880",
            "inspection-status-message-tv.xml, 25082200013, 汤钞棣, TV, %VisitId%, %ClinicId%, '', 335969, 华珊, 2025-08-22 15:21:16, 100020"
    })
    void testParseValidXml(String filename, String patientId, String patientName,
                           PatientType patientType, String visitId, String cardNo,
                           String admissionNo, String requestId, String operator,
                           String operationTime, String itemCode) throws IOException {

        String xml = readTestResource(filename);
        InspectionStatusMessage message = InspectionStatusMessage.Parser.parseFromXml(xml);

        assertNotNull(message, "解析结果不能为空");
        assertBasicMessage(message);
        assertPatientInfo(message, patientId, patientName, patientType);
        assertVisitInfo(message, visitId, cardNo, admissionNo);

        // 验证业务字段
        assertEquals(requestId, message.getRequestId());
        assertEquals(operator, message.getOperator());
        assertEquals("医学影像科", message.getExecutingDepartmentName());
        assertEquals(LocalDateTime.parse(operationTime, DATE_TIME_FORMATTER), message.getOperationTime());

        // 验证项目数据
        assertTrue(message.getContent().contains(itemCode));
        assertEquals(xml, message.getRawText());
    }

    /**
     * 异常输入测试：所有无效输入都应该返回null
     * 合并了原来4个冗余的异常测试方法
     */
    @ParameterizedTest
    @ValueSource(strings = {
            "", "   ", "<invalid>xml</invalid>",
            "<BSXml><MsgHeader><Organization>test</Organization></MsgHeader></BSXml>",
            "<BSXml><MsgBody><Patient><SourcePatientId>123</SourcePatientId></Patient></MsgBody></BSXml>"
    })
    void testParseInvalidXml(String invalidXml) {
        assertNull(InspectionStatusMessage.Parser.parseFromXml(invalidXml));
    }

    @Test
    void testParseNullXml() {
        assertNull(InspectionStatusMessage.Parser.parseFromXml(null));
    }

    /**
     * 边界情况测试：验证解析器的容错能力
     */
    @Test
    void testParseBoundaryConditions() throws IOException {
        String xml = readTestResource("inspection-status-message-iv.xml");

        // 测试无效PatientType - 应该默认为TV
        String invalidTypeXml = xml.replace("<SourcePatientIdType>IV</SourcePatientIdType>",
                "<SourcePatientIdType>INVALID</SourcePatientIdType>");
        InspectionStatusMessage message = InspectionStatusMessage.Parser.parseFromXml(invalidTypeXml);
        assertNotNull(message);
        assertEquals(PatientType.TV, message.getPatientType());

        // 测试无效操作时间 - 应该使用当前时间
        String invalidTimeXml = xml.replace("<OperatingDateTime>2025-08-22 15:22:32</OperatingDateTime>",
                "<OperatingDateTime>invalid-date</OperatingDateTime>");
        message = InspectionStatusMessage.Parser.parseFromXml(invalidTimeXml);
        assertNotNull(message);
        assertNotNull(message.getOperationTime());
    }
}
