package space.lzhq.ph.webservice;

/**
 * BSXml响应生成器
 */
public class BSXmlResponse {

    private BSXmlResponse() {
    }

    // 常量集中管理，消除硬编码
    private static final String SENDER = "YQTL";
    private static final String MSG_VERSION = "3.1";

    // XML模板 - 一个模板统治所有情况
    private static final String XML_TEMPLATE =
            "<BSXml>" +
                    "<MsgHeader>" +
                    "<Sender>%s</Sender>" +
                    "<MsgType>%s</MsgType>" +
                    "<MsgVersion>%s</MsgVersion>" +
                    "<Status>%s</Status>" +
                    "<Detail>%s</Detail>" +
                    "</MsgHeader>" +
                    "</BSXml>";

    /**
     * 生成成功响应XML
     *
     * @param detail 成功详情
     * @return XML字符串
     */
    public static String success(String msgType, String detail) {
        return String.format(XML_TEMPLATE,
                SENDER, msgType, MSG_VERSION,
                "true", detail != null ? detail : "操作成功");
    }

    /**
     * 生成失败响应XML
     *
     * @param detail 失败详情
     * @return XML字符串
     */
    public static String failure(String msgType, String detail) {
        return String.format(XML_TEMPLATE,
                SENDER, msgType, MSG_VERSION,
                "false", detail != null ? detail : "操作失败");
    }

    /**
     * 生成异常响应XML
     *
     * @param exception 异常对象
     * @return XML字符串
     */
    public static String error(String msgType, Exception exception) {
        String detail = exception != null ?
                "系统异常: " + exception.getMessage() : "未知系统异常";
        return failure(msgType, detail);
    }
}