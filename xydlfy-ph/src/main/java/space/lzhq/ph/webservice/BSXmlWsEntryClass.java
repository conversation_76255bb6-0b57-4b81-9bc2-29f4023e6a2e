package space.lzhq.ph.webservice;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebService;

@WebService(targetNamespace = "http://ws.access.hai/")
public interface BSXmlWsEntryClass {

    /**
     * 停诊通知
     */
    String SERVICE_TYPE_OUT_APPOINT_SEND_STOP = "OutAppointSend_Stop";

    /**
     * 检查状态更新
     */
    String SERVICE_TYPE_INSPECTION_STATUS_UPDATE = "InspectionStatusUpdate";

    /**
     * 检验报告审核
     */
    String SERVICE_TYPE_LAB_REPORT_AUDIT = "LabReportAudit";

    @WebMethod
    String invoke(
            @WebParam(name = "service") String service,
            @WebParam(name = "urid") String urid,
            @WebParam(name = "pwd") String pwd,
            @WebParam(name = "parameter") String parameter
    );
} 