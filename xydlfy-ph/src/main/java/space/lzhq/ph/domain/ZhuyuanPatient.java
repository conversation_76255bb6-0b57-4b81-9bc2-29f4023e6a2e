package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 住院患者对象 ph_zhuyuan_patient
 * HIS接口返回的住院患者信息示例如下：
 * <pre>
 * {
 *     "admissionNo": "210607",
 *     "hospitalId": "1",
 *     "patientId": "1249682",
 *     "admissionNumber": "266141",
 *     "archivesNumber": "266141",
 *     "patientType": "6041",
 *     "patientName": "刘中伊",
 *     "patientSex": "1",
 *     "patientBirthday": "2013-06-20",
 *     "patientIdCard": "652325201306200214",
 *     "patientNation": "3",
 *     "patientDepartMent": "211",
 *     "departmentName": "重症医学科(PICU)",
 *     "patientPhone": "17767624421",
 *     "patientBedNumber": "ZZ17",
 *     "admissionTime": "2025-08-21 18:55:00",
 *     "inpatientDoctor": "3141",
 *     "startTime": "2025-08-22 00:53:12",
 *     "balance": "47.51",
 *     "leaveStatus": "0"
 * }
 * </pre>
 */
@Data
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class ZhuyuanPatient extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @EqualsAndHashCode.Include
    private Long id;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 患者编号，示例值：1249682
     * HIS字段为：patientId
     */
    private String patientNo;

    /**
     * 住院号，示例值：210607
     * HIS字段为：admissionNo
     */
    private String zhuyuanNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 科室编码
     */
    private String departmentCode;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 床号
     */
    private String bedNo;

    /**
     * 住院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime comeTime;

    /**
     * 出院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveTime;

    /**
     * 状态
     */
    private String state;

    private String openId;

    private String unionId;

    /**
     * 住院号码，示例值：266141
     * HIS字段为：admissionNumber
     */
    private String admissionNumber;

    /**
     * 住院余额
     */
    private BigDecimal balance;

}
