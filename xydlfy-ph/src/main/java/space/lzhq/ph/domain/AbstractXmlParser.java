package space.lzhq.ph.domain;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 抽象XML解析器基类 - 消除Parser间的重复代码
 */
@Slf4j
public abstract class AbstractXmlParser {

    AbstractXmlParser() {
    }

    /**
     * 通用容错解析方法 - 消除重复的try-catch模式
     */
    protected static <T> T parseWithFallback(String value, Function<String, T> parser,
                                             T fallback, String fieldName) {
        if (StringUtils.isEmpty(value)) {
            log.debug("字段{}为空，使用默认值", fieldName);
            return fallback;
        }

        try {
            return parser.apply(value);
        } catch (Exception e) {
            log.warn("无法解析{}: {}", fieldName, value);
            return fallback;
        }
    }

    /**
     * 解析XML文档并返回根元素
     */
    protected static Element parseXmlToRoot(String xml) {
        try {
            return DocumentHelper.parseText(xml).getRootElement();
        } catch (Exception e) {
            log.error("解析XML失败：{}", xml, e);
            return null;
        }
    }

    /**
     * 通用字段映射方法
     */
    protected static <T> void mapFields(T target, Element element,
                                        Map<String, BiConsumer<T, String>> mapping) {
        mapping.forEach((xmlField, setter) ->
                setter.accept(target, element.elementTextTrim(xmlField)));
    }

    /**
     * 验证XML输入
     */
    protected static void validateXmlInput(String xml) {
        if (StringUtils.isBlank(xml)) {
            log.error("XML为空");
            throw new IllegalArgumentException("XML不能为空");
        }
    }
}
