package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonGetter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * 门诊停诊消息
 */
@Data
@TableName("ph_outpatient_stop_clinic")
public class OutpatientStopClinic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 排班ID
     */
    private String scheduleMark;

    /**
     * 预约ID，具有唯一性
     *
     * @see Reservation#getReservationNumber()
     */
    private String appointmentId;

    /**
     * 病人ID
     */
    private String sourcePatientId;

    /**
     * 挂号时间
     */
    private LocalDateTime registeredDateTime;

    /**
     * 预约时间（就诊时间）
     */
    private LocalDateTime appointmentDateTime;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 挂号科室ID
     */
    private String registeredDept;

    /**
     * 挂号科室名称
     */
    private String registeredDeptName;

    /**
     * 挂号医生ID
     */
    private String registeredDoctor;

    /**
     * 挂号医生姓名
     */
    private String registeredDoctorName;

    /**
     * 操作人姓名
     */
    private String operatingName;

    /**
     * 停诊操作时间
     */
    private LocalDateTime operatingDateTime;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 微信用户ID
     */
    private String openId;

    /**
     * 订阅消息发送时间
     */
    private LocalDateTime subscribeMessageSendTime;

    /**
     * 订阅消息发送状态 0-成功 1-失败
     */
    private Boolean subscribeMessageSendStatus;

    /**
     * 订阅消息发送失败原因
     */
    private String subscribeMessageError;

    @JsonGetter("stopType")
    public String getStopType() {
        if (this.getMessageContent().contains("替诊")) {
            return "替诊";
        }

        return "停诊";
    }

    /**
     * 门诊停诊消息XML解析器
     */
    @Slf4j
    public static class Parser extends AbstractXmlParser {

        private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 字段映射表：消除硬编码的setter调用
        private static final Map<String, BiConsumer<OutpatientStopClinic, String>> STRING_FIELD_MAPPING = Map.of(
                "ScheduleMark", OutpatientStopClinic::setScheduleMark,
                "AppointmentId", OutpatientStopClinic::setAppointmentId,
                "SourcePatientId", OutpatientStopClinic::setSourcePatientId,
                "IdCard", OutpatientStopClinic::setIdCard,
                "Name", OutpatientStopClinic::setName,
                "RegisteredDept", OutpatientStopClinic::setRegisteredDept,
                "RegisteredDeptNane", OutpatientStopClinic::setRegisteredDeptName, // XML中的拼写错误，保持原样
                "RegisteredDcotor", OutpatientStopClinic::setRegisteredDoctor,     // XML中的拼写错误，保持原样
                "RegisteredDcotorName", OutpatientStopClinic::setRegisteredDoctorName, // XML中的拼写错误，保持原样
                "OperatingName", OutpatientStopClinic::setOperatingName
        );

        // 时间字段映射表
        private static final Map<String, BiConsumer<OutpatientStopClinic, LocalDateTime>> DATETIME_FIELD_MAPPING = Map.of(
                "RegisteredDateTime", OutpatientStopClinic::setRegisteredDateTime,
                "AppointmentDateTime", OutpatientStopClinic::setAppointmentDateTime,
                "OperatingDateTime", OutpatientStopClinic::setOperatingDateTime
        );

        private Parser() {
            super();
        }

        /**
         * 从XML字符串解析门诊停诊消息
         *
         * @param xml XML字符串
         * @return 解析后的门诊停诊消息对象
         */
        @NotNull
        public static OutpatientStopClinic parseFromXml(@NotNull String xml) {
            validateXmlInput(xml);

            Element root = parseXmlToRoot(xml);
            if (root == null) {
                throw new RuntimeException("XML解析失败");
            }

            Element visit = root.element("Visit");
            if (visit == null) {
                throw new RuntimeException("Visit元素不存在");
            }

            OutpatientStopClinic stopClinic = new OutpatientStopClinic();

            mapStringFields(stopClinic, visit);
            mapDateTimeFields(stopClinic, visit);
            mapSpecialFields(stopClinic, visit);

            return stopClinic;
        }

        /**
         * 映射字符串字段
         */
        private static void mapStringFields(OutpatientStopClinic stopClinic, Element visit) {
            mapFields(stopClinic, visit, STRING_FIELD_MAPPING);
        }

        /**
         * 映射时间字段
         */
        private static void mapDateTimeFields(OutpatientStopClinic stopClinic, Element visit) {
            DATETIME_FIELD_MAPPING.forEach((xmlField, setter) -> {
                LocalDateTime dateTime = parseWithFallback(
                        visit.elementTextTrim(xmlField),
                        time -> LocalDateTime.parse(time, DATE_TIME_FORMATTER),
                        LocalDateTime.now(),
                        xmlField
                );
                setter.accept(stopClinic, dateTime);
            });
        }

        /**
         * 映射特殊字段
         */
        private static void mapSpecialFields(OutpatientStopClinic stopClinic, Element visit) {
            stopClinic.setMessageContent(visit.elementTextTrim("messageContent"));
            stopClinic.setCreateTime(LocalDateTime.now());
            stopClinic.setIsRead(false);
        }
    }
}
