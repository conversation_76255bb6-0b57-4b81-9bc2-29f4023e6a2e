package space.lzhq.ph.service

import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.util.UriComponentsBuilder

/**
 * RadOnline服务 - Token管理和URL构建
 *
 * <AUTHOR>
 */
@Service
class RadOnlineService(
    private val sysConfigService: ISysConfigService,
    private val encryptionService: RadOnlineEncryptionService,
    private val radOnlineHttpService: RadOnlineHttpService
) {

    private companion object {
        // 配置键常量
        const val CONFIG_KEY_USERNAME = "mobile-imaging.username"
        const val CONFIG_KEY_PASSWORD = "mobile-imaging.password"
        const val CONFIG_KEY_UNIT_ID = "mobile-imaging.unit-id"
        const val CONFIG_KEY_BASE_URL = "mobile-imaging.base-url"

        // URL查询参数常量
        const val URL_PARAM_TOKEN = "token"
        const val URL_PARAM_UNIT_ID = "unitid"
        const val URL_PARAM_ENCRYPTED_GUID = "xeguid"
        const val URL_PARAM_HAS_LIST = "hasList"
        const val URL_PARAM_CALLBACK = "callBack"
        const val URL_PARAM_MOI_CODE = "moiCode"
        const val MOI_CODE = "M1206"

        // URL路径常量
        const val URL_PATH_PATIENT = "/web/fore-end/patient.html"

        // URL片段常量
        const val URL_FRAGMENT_CHECK_DETAIL = "/check-detail"
    }

    private val logger = LoggerFactory.getLogger(RadOnlineService::class.java)

    /**
     * 获取访问Token
     * @return Token字符串，失败时返回null
     */
    fun getToken(): String? {
        val username = sysConfigService.selectConfigByKey(CONFIG_KEY_USERNAME)
        val password = sysConfigService.selectConfigByKey(CONFIG_KEY_PASSWORD)

        if (username.isNullOrBlank() || password.isNullOrBlank()) {
            logger.warn(
                "RadOnline service configuration is incomplete. One or more of '{}', '{}' is missing.",
                CONFIG_KEY_USERNAME, CONFIG_KEY_PASSWORD
            )
            return null
        }

        return try {
            val tokenResponse = runBlocking {
                radOnlineHttpService.getToken(RadOnlineTokenForm(username, password))
            }

            if (tokenResponse.success && !tokenResponse.data.isNullOrBlank()) {
                logger.info("RadOnline Token获取成功")
                tokenResponse.data
            } else {
                logger.warn(
                    "Token获取失败: msg={}, subCode={}, subMsg={}",
                    tokenResponse.msg,
                    tokenResponse.subCode,
                    tokenResponse.subMsg
                )
                null
            }
        } catch (e: Exception) {
            logger.warn("Token获取请求失败", e)
            null
        }
    }

    /**
     * 获取移动影像访问URL（一站式服务）
     *
     * @param patientId 患者标识符（身份证号或就诊卡号）
     * @return 完整的移动影像访问URL，失败时返回null
     */
    fun getMobileImagingUrl(patientId: String): String? {
        if (patientId.isBlank()) {
            logger.warn("获取移动影像URL失败：患者ID为空")
            return null
        }

        // 加密患者标识符
        val encryptedPatientId = try {
            encryptionService.encryptExamId(patientId)
        } catch (e: Exception) {
            when (e) {
                is RadOnlineEncryptionException -> logger.warn("获取移动影像URL失败：患者ID加密失败", e)
                else -> logger.warn("获取移动影像URL失败：患者ID加密时发生未知错误", e)
            }
            return null
        }

        // 获取访问令牌
        val token = getToken() ?: run {
            logger.warn("获取移动影像URL失败：无法获取访问令牌")
            return null
        }

        // 构建最终URL
        return buildMobileImagingUrl(token, encryptedPatientId)
    }

    /**
     * 构建移动影像访问URL（内部方法）
     *
     * @param token RadOnline访问令牌
     * @param encryptedPatientId 加密后的患者标识符
     * @return 完整的移动影像访问URL，失败时返回null
     */
    private fun buildMobileImagingUrl(token: String, encryptedPatientId: String): String? {
        if (token.isBlank() || encryptedPatientId.isBlank()) {
            logger.warn("构建移动影像URL失败：token或患者ID为空")
            return null
        }

        val unitId = sysConfigService.selectConfigByKey(CONFIG_KEY_UNIT_ID)
        val baseUrl = sysConfigService.selectConfigByKey(CONFIG_KEY_BASE_URL)

        val missingKeys = buildList {
            if (unitId.isNullOrBlank()) add(CONFIG_KEY_UNIT_ID)
            if (baseUrl.isNullOrBlank()) add(CONFIG_KEY_BASE_URL)
        }
        if (missingKeys.isNotEmpty()) {
            logger.warn(
                "移动影像URL构建配置不完整。缺少配置项：{}",
                missingKeys.joinToString()
            )
            return null
        }

        return try {
            UriComponentsBuilder.fromUriString(baseUrl)
                .path(URL_PATH_PATIENT)
                .queryParam(URL_PARAM_TOKEN, token)
                .queryParam(URL_PARAM_UNIT_ID, unitId)
                .queryParam(URL_PARAM_ENCRYPTED_GUID, encryptedPatientId)
                .queryParam(URL_PARAM_HAS_LIST, true)
                .queryParam(URL_PARAM_CALLBACK, true)
                .queryParam(URL_PARAM_MOI_CODE, MOI_CODE)
                .fragment(URL_FRAGMENT_CHECK_DETAIL)
                .build(false)   // 厂商实现的有点奇怪，这里不能编码；编码后，反而无法解密
                .toUriString()
        } catch (e: Exception) {
            logger.warn("构建移动影像URL失败", e)
            null
        }
    }
}