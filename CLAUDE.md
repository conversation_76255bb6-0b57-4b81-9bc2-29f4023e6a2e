# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java/Kotlin web application for Xinjiang Children's Hospital built on the RuoYi framework (version 4.7.7). It's a multi-module Maven project using Spring Boot 3.5.4, Java 21, MySQL, Redis, and includes WeChat/Alipay payment integrations.

## Architecture

**Modules:**
- `ruoyi-admin`: Main web application entry point
- `ruoyi-framework`: Core framework components
- `ruoyi-system`: System management functionality
- `ruoyi-quartz`: Scheduled tasks with Quartz
- `ruoyi-generator`: Code generation tools
- `ruoyi-common`: Common utilities
- `xydlfy-ph`: Hospital-specific business logic (Kotlin)
- `ph-common`: Common utilities for xydlfy-ph module

**Key Technologies:**
- Spring Boot 3.5.4, Spring Security, MyBatis Plus
- Kotlin 2.2.0 with coroutines
- MySQL, Redis, Druid connection pool
- WeChat/Alipay payment integrations
- Thymeleaf templates, Bootstrap frontend

## Development Commands

**Build the project:**
```bash
mvn clean package -Dmaven.test.skip=true
```

**Build with tests:**
```bash
mvn clean package
```

**Run specific tests:**
```bash
mvn test -Dtest=BSXmlWsEntryClassTest
mvn test -Dtest=*Test -pl ruoyi-admin
```

**Run application locally:**
```bash
mvn spring-boot:run -pl ruoyi-admin
```

**Development profiles:** Use `druid` profile for local development with H2 database:
```bash
mvn spring-boot:run -pl ruoyi-admin -Dspring.profiles.active=druid
```

## Testing

**Test Structure:**
- Tests are in each module's `src/test` directory
- Uses JUnit 5 with Spring Boot Test
- Test profiles: `test` and `druid`
- Limited local testing due to hospital network dependencies

**Key Test Files:**
- `ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/BSXmlWsEntryClassTest.java`
- `ruoyi-admin/src/test/java/com/ruoyi/xyd6fy/PacsReportServiceTest.java`
- `xydlfy-ph/src/test/kotlin/space/lzhq/ph/service/RadOnlineServiceTest.kt`

## Deployment

**Production deployment uses:**
- `./deploy.sh`: Full build and deployment script
- `zyxcx.service`: Systemd service definition
- `rollback.sh`: Rollback script for failed deployments

**Note:** Production deployment requires specific hospital network access and should not be run locally.

## Code Style & Conventions

- Java 21 with Lombok and MapStruct
- Kotlin code in `xydlfy-ph` module
- Maven enforcer plugin for dependency validation
- AliCloud Maven repository for dependencies
- No explicit linting/checkstyle configuration found

## Important Configuration Files

- `application.yml`: Main configuration
- `application-druid.yml`: Database configuration
- `application-prod.yml`: Production settings
- `alipay.yaml`: Payment configuration
- Various hospital-specific YAML configs

## Database

- Schema files in `ddl/ph/` directory
- Uses MySQL with full-text search configured
- Quartz scheduler tables included

## Security Notes

- Contains payment integration configurations
- Uses proper security practices (HttpOnly cookies, secure flags)
- No sensitive data should be committed to repository